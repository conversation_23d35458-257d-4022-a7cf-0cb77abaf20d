#!/usr/bin/env python3
"""
简单的GUI测试程序
"""
import tkinter as tk
from tkinter import ttk, messagebox
import os

def test_gui():
    """测试GUI是否能正常显示"""
    # 设置环境变量
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    # 创建主窗口
    root = tk.Tk()
    root.title("GUI测试 - 交易复盘工具")
    root.geometry("600x400")
    
    # 确保窗口显示在前台
    root.lift()
    root.attributes('-topmost', True)
    root.after_idle(root.attributes, '-topmost', False)
    
    # 创建标签
    label = ttk.Label(root, text="🎉 GUI测试成功！", font=('Arial', 16))
    label.pack(pady=50)
    
    # 创建按钮
    def show_message():
        messagebox.showinfo("测试", "GUI功能正常！")
    
    button = ttk.Button(root, text="点击测试", command=show_message)
    button.pack(pady=20)
    
    # 创建文本
    text_info = tk.Text(root, height=10, width=60)
    text_info.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
    
    info_text = """
如果您能看到这个窗口，说明Tkinter GUI正常工作！

接下来您可以：
1. 关闭这个测试窗口
2. 运行完整的交易复盘工具：python3 main.py
3. 或者使用启动脚本：./start.sh

如果仍然看不到主程序窗口，可能的原因：
- 窗口被隐藏在其他应用程序后面
- 需要在Dock中查找Python图标
- macOS安全设置阻止了应用程序显示
    """
    
    text_info.insert(1.0, info_text)
    text_info.config(state=tk.DISABLED)
    
    # 强制获得焦点
    root.focus_force()
    
    print("GUI测试窗口已启动")
    print("请查看是否有窗口显示")
    
    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    test_gui()
