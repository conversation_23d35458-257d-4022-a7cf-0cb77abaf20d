@echo off
REM 交易复盘工具启动脚本 (Windows)

echo 正在启动交易复盘工具...

REM 检查Python版本
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
) else (
    python3 --version >nul 2>&1
    if %errorlevel% equ 0 (
        set PYTHON_CMD=python3
    ) else (
        echo 错误: 未找到Python解释器
        pause
        exit /b 1
    )
)

REM 检查依赖
echo 检查依赖项...
%PYTHON_CMD% -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装pandas...
    pip install pandas
)

REM 设置环境变量以抑制Tkinter警告
set TK_SILENCE_DEPRECATION=1

REM 启动应用程序
echo 启动应用程序...
%PYTHON_CMD% main.py

pause
