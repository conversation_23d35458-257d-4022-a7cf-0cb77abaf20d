"""
简化的GUI界面模块
"""
import tkinter as tk
from tkinter import ttk

class SimpleTradingApp:
    def __init__(self, root):
        print("初始化SimpleTradingApp...")
        self.root = root
        self.create_widgets()
        print("SimpleTradingApp初始化完成")

    def create_widgets(self):
        """创建主要组件"""
        print("创建简化的界面组件...")
        
        # 创建状态栏
        self.status_bar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        print("状态栏创建完成")

        # 创建主要的标签页
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        print("标签页容器创建完成")

        # 创建简单的标签页
        self.create_simple_tab()
        print("简单标签页创建完成")

    def create_simple_tab(self):
        """创建简单的标签页"""
        # 第一个标签页
        frame1 = ttk.Frame(self.notebook)
        self.notebook.add(frame1, text="添加交易")
        
        # 添加一些简单的组件
        label = tk.Label(frame1, text="交易复盘工具", font=("Arial", 16))
        label.pack(pady=20)
        
        # 添加一些输入框
        input_frame = ttk.LabelFrame(frame1, text="交易信息", padding="10")
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(input_frame, text="仓位大小:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.position_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.position_var, width=20).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(input_frame, text="交易方向:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.direction_var = tk.StringVar()
        ttk.Combobox(input_frame, textvariable=self.direction_var, values=["多头", "空头"], width=17).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 按钮
        button_frame = ttk.Frame(frame1)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="保存交易", command=self.save_trade).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空表单", command=self.clear_form).pack(side=tk.LEFT)
        
        # 第二个标签页
        frame2 = ttk.Frame(self.notebook)
        self.notebook.add(frame2, text="交易历史")
        
        label2 = tk.Label(frame2, text="交易历史列表", font=("Arial", 14))
        label2.pack(pady=20)
        
        # 第三个标签页
        frame3 = ttk.Frame(self.notebook)
        self.notebook.add(frame3, text="统计分析")
        
        label3 = tk.Label(frame3, text="统计分析图表", font=("Arial", 14))
        label3.pack(pady=20)

    def save_trade(self):
        """保存交易"""
        print("保存交易按钮被点击")
        self.status_bar.config(text="交易已保存")

    def clear_form(self):
        """清空表单"""
        print("清空表单按钮被点击")
        self.position_var.set("")
        self.direction_var.set("")
        self.status_bar.config(text="表单已清空")
