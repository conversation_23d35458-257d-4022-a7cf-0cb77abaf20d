"""
数据模型和操作模块
"""
from datetime import datetime
import pandas as pd
from database import DatabaseManager

class TradeModel:
    def __init__(self):
        self.db = DatabaseManager()
    
    def add_trade(self, trade_data):
        """添加新的交易记录"""
        query = '''
            INSERT INTO trades (
                position_size, direction, take_profit_pct, stop_loss_pct,
                entry_time, exit_time, entry_fee_pct, exit_fee_pct,
                instrument, strategy, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        params = (
            trade_data['position_size'],
            trade_data['direction'],
            trade_data['take_profit_pct'],
            trade_data['stop_loss_pct'],
            trade_data['entry_time'],
            trade_data['exit_time'],
            trade_data['entry_fee_pct'],
            trade_data['exit_fee_pct'],
            trade_data.get('instrument', ''),
            trade_data.get('strategy', ''),
            trade_data.get('notes', '')
        )
        
        return self.db.execute_update(query, params)
    
    def get_all_trades(self):
        """获取所有交易记录"""
        query = '''
            SELECT * FROM trades ORDER BY entry_time DESC
        '''
        results = self.db.execute_query(query)
        
        if not results:
            return pd.DataFrame()
        
        columns = [
            'id', 'position_size', 'direction', 'take_profit_pct', 'stop_loss_pct',
            'entry_time', 'exit_time', 'entry_fee_pct', 'exit_fee_pct',
            'instrument', 'strategy', 'notes', 'created_at'
        ]
        
        return pd.DataFrame(results, columns=columns)
    
    def get_trades_by_date_range(self, start_date, end_date):
        """根据日期范围获取交易记录"""
        query = '''
            SELECT * FROM trades 
            WHERE entry_time BETWEEN ? AND ?
            ORDER BY entry_time DESC
        '''
        results = self.db.execute_query(query, (start_date, end_date))
        
        if not results:
            return pd.DataFrame()
        
        columns = [
            'id', 'position_size', 'direction', 'take_profit_pct', 'stop_loss_pct',
            'entry_time', 'exit_time', 'entry_fee_pct', 'exit_fee_pct',
            'instrument', 'strategy', 'notes', 'created_at'
        ]
        
        return pd.DataFrame(results, columns=columns)
    
    def update_trade(self, trade_id, trade_data):
        """更新交易记录"""
        query = '''
            UPDATE trades SET
                position_size = ?, direction = ?, take_profit_pct = ?, stop_loss_pct = ?,
                entry_time = ?, exit_time = ?, entry_fee_pct = ?, exit_fee_pct = ?,
                instrument = ?, strategy = ?, notes = ?
            WHERE id = ?
        '''
        
        params = (
            trade_data['position_size'],
            trade_data['direction'],
            trade_data['take_profit_pct'],
            trade_data['stop_loss_pct'],
            trade_data['entry_time'],
            trade_data['exit_time'],
            trade_data['entry_fee_pct'],
            trade_data['exit_fee_pct'],
            trade_data.get('instrument', ''),
            trade_data.get('strategy', ''),
            trade_data.get('notes', ''),
            trade_id
        )
        
        return self.db.execute_update(query, params)
    
    def delete_trade(self, trade_id):
        """删除交易记录"""
        query = 'DELETE FROM trades WHERE id = ?'
        return self.db.execute_update(query, (trade_id,))
    
    def get_trade_by_id(self, trade_id):
        """根据ID获取单个交易记录"""
        query = 'SELECT * FROM trades WHERE id = ?'
        result = self.db.execute_query(query, (trade_id,))
        
        if not result:
            return None
        
        columns = [
            'id', 'position_size', 'direction', 'take_profit_pct', 'stop_loss_pct',
            'entry_time', 'exit_time', 'entry_fee_pct', 'exit_fee_pct',
            'instrument', 'strategy', 'notes', 'created_at'
        ]
        
        return dict(zip(columns, result[0]))

class SettingsModel:
    def __init__(self):
        self.db = DatabaseManager()
    
    def get_setting(self, key):
        """获取设置值"""
        query = 'SELECT value FROM settings WHERE key = ?'
        result = self.db.execute_query(query, (key,))
        return result[0][0] if result else None
    
    def set_setting(self, key, value):
        """设置值"""
        query = '''
            INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)
        '''
        return self.db.execute_update(query, (key, value))
    
    def get_all_settings(self):
        """获取所有设置"""
        query = 'SELECT key, value FROM settings'
        results = self.db.execute_query(query)
        return dict(results) if results else {}
    
    def get_default_values(self):
        """获取默认值"""
        settings = self.get_all_settings()
        return {
            'position_size': float(settings.get('default_position_size', 1000)),
            'entry_fee': float(settings.get('default_entry_fee', 0.1)),
            'exit_fee': float(settings.get('default_exit_fee', 0.1)),
            'take_profit': float(settings.get('default_take_profit', 2.0)),
            'stop_loss': float(settings.get('default_stop_loss', 1.0))
        }
