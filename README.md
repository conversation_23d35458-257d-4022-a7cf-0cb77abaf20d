# 交易复盘工具

一个功能完整的本地桌面交易复盘应用程序，帮助交易者记录、分析和优化交易表现。

## 功能特性

### 📊 数据录入模块
- **必填字段**：仓位大小、交易方向、止盈/止损百分比、入场/出场时间、手续费
- **可选字段**：交易品种、策略说明、备注
- **智能默认值**：支持用户自定义默认值，快速填充常用配置
- **实时验证**：表单数据实时验证，防止无效数据录入

### 📈 计算引擎
- **单笔交易指标**：盈亏金额和百分比、总手续费、净盈亏、风险回报比
- **统计分析**：胜率、累计盈亏、平均盈利/亏损、盈利因子、最大回撤
- **连续统计**：最大连胜/连败次数

### 📋 数据管理
- **CRUD操作**：完整的增删改查功能
- **高级过滤**：按日期范围、方向、盈亏状态筛选
- **数据导出**：支持CSV格式导出
- **数据备份**：SQLite数据库，安全可靠

### 🎨 用户界面
- **标签页设计**：添加交易、交易历史、统计分析、设置四大模块
- **响应式布局**：支持窗口缩放，适配不同屏幕尺寸
- **快捷键支持**：Ctrl+N新建、Ctrl+S保存等
- **状态反馈**：实时状态栏和消息提示

## 技术架构

### 技术栈
- **语言**：Python 3.8+
- **GUI框架**：Tkinter（内置，跨平台）
- **数据库**：SQLite
- **数据处理**：pandas
- **架构模式**：MVC（Model-View-Controller）

### 文件结构
```
交易复盘工具/
├── main.py              # 应用程序入口
├── database.py          # 数据库架构和连接管理
├── model.py             # 数据模型和操作
├── view.py              # GUI界面组件
├── controller.py        # 业务逻辑和事件处理
├── calculator.py        # 交易指标计算
├── requirements.txt     # 依赖项
├── README.md           # 说明文档
└── trading_review.db   # SQLite数据库文件（运行后生成）
```

## 安装和运行

### 环境要求
- Python 3.8 或更高版本
- pip 包管理器

### 安装步骤

1. **下载项目文件**
   - 下载所有项目文件到本地文件夹

2. **快速启动（推荐）**

   **macOS/Linux:**
   ```bash
   ./start.sh
   ```

   **Windows:**
   ```cmd
   start.bat
   ```

3. **手动安装和运行**

   **安装依赖:**
   ```bash
   pip install -r requirements.txt
   # 或者
   pip3 install pandas
   ```

   **运行应用程序:**
   ```bash
   python main.py
   # 或者
   python3 main.py
   ```

### 首次运行
- 应用程序会自动创建SQLite数据库文件
- 默认设置会被初始化
- 可以立即开始录入交易数据

## 使用指南

### 添加交易记录
1. 点击"添加交易"标签页
2. 填写必填字段（标有*号）：
   - **仓位大小**: 例如 1000（投入资金）
   - **交易方向**: 选择"多头"或"空头"
   - **止盈百分比**: 例如 2.0（表示2%）
   - **止损百分比**: 例如 1.0（表示1%）
   - **入场时间**: 格式 YYYY-MM-DD HH:MM:SS
   - **出场时间**: 格式 YYYY-MM-DD HH:MM:SS
   - **手续费**: 默认0.1%，可根据实际调整
3. 可选填写交易品种、策略等信息
4. 点击"保存交易"按钮

### 查看交易历史
1. 点击"交易历史"标签页
2. 使用日期过滤器筛选特定时间段（格式：YYYY-MM-DD）
3. 选中记录后点击"编辑选中"可修改
4. 选中记录后点击"删除选中"可删除

### 统计分析
1. 点击"统计分析"标签页
2. 查看各项交易指标：
   - **胜率**: 盈利交易占总交易的百分比
   - **盈利因子**: 总盈利/总亏损的比值
   - **最大回撤**: 从峰值到谷值的最大损失
3. 点击"刷新统计"更新数据

### 设置默认值
1. 点击"设置"标签页
2. 修改默认值配置（新交易时自动填充）
3. 点击"保存设置"

### 使用技巧
- **快捷键**: Ctrl+N 新建交易，Ctrl+S 保存交易
- **时间输入**: 点击"现在"按钮快速填入当前时间
- **批量操作**: 可导出CSV进行批量分析
- **数据备份**: 定期备份 `trading_review.db` 文件

## 数据库结构

### 交易记录表 (trades)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键，自增 |
| position_size | REAL | 仓位大小 |
| direction | TEXT | 交易方向（多头/空头） |
| take_profit_pct | REAL | 止盈百分比 |
| stop_loss_pct | REAL | 止损百分比 |
| entry_time | DATETIME | 入场时间 |
| exit_time | DATETIME | 出场时间 |
| entry_fee_pct | REAL | 入场手续费百分比 |
| exit_fee_pct | REAL | 出场手续费百分比 |
| instrument | TEXT | 交易品种 |
| strategy | TEXT | 交易策略 |
| notes | TEXT | 备注 |
| created_at | DATETIME | 创建时间 |

### 设置表 (settings)
| 字段 | 类型 | 说明 |
|------|------|------|
| key | TEXT | 设置键名 |
| value | TEXT | 设置值 |

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| Ctrl+N | 新建交易 |
| Ctrl+S | 保存交易 |
| Delete | 删除选中交易 |
| F5 | 刷新数据 |

## 常见问题

### Q: 如何备份数据？
A: 复制 `trading_review.db` 文件即可备份所有交易数据。

### Q: 可以在多台电脑上使用吗？
A: 可以，只需复制整个项目文件夹和数据库文件。

### Q: 支持哪些操作系统？
A: 支持 Windows、macOS 和 Linux，因为使用的是跨平台的 Python 和 Tkinter。

### Q: 如何导入历史数据？
A: 目前支持手动录入，未来版本将支持CSV导入功能。

## 开发计划

- [ ] 添加图表可视化功能
- [ ] 支持CSV数据导入
- [ ] 添加更多统计指标
- [ ] 支持多币种和汇率转换
- [ ] 添加交易日志功能
- [ ] 支持数据同步到云端

## 技术支持

如果遇到问题或有功能建议，请：
1. 检查是否满足环境要求
2. 确认所有依赖已正确安装
3. 查看控制台错误信息

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**交易复盘工具** - 让每一笔交易都成为成长的阶梯 📈
