"""
控制器模块 - 业务逻辑和事件处理
"""
import csv
from datetime import datetime
from tkinter import filedialog
import pandas as pd
from model import TradeModel, SettingsModel
from calculator import TradingCalculator

class TradingController:
    def __init__(self, view):
        self.view = view
        self.trade_model = TradeModel()
        self.settings_model = SettingsModel()
        self.current_edit_id = None
        
        # 初始化数据
        self.refresh_data()
        self.load_settings()
    
    def validate_form_data(self, data):
        """验证表单数据"""
        errors = []
        
        # 验证必填字段
        required_fields = {
            'position_size': '仓位大小',
            'direction': '交易方向',
            'take_profit_pct': '止盈百分比',
            'stop_loss_pct': '止损百分比',
            'entry_time': '入场时间',
            'exit_time': '出场时间',
            'entry_fee_pct': '入场手续费',
            'exit_fee_pct': '出场手续费'
        }
        
        for field, name in required_fields.items():
            if not data.get(field) or data[field].strip() == '':
                errors.append(f"{name}不能为空")
        
        # 验证数值字段
        numeric_fields = {
            'position_size': '仓位大小',
            'take_profit_pct': '止盈百分比',
            'stop_loss_pct': '止损百分比',
            'entry_fee_pct': '入场手续费',
            'exit_fee_pct': '出场手续费'
        }
        
        for field, name in numeric_fields.items():
            if data.get(field):
                try:
                    value = float(data[field])
                    if value < 0:
                        errors.append(f"{name}不能为负数")
                    if field in ['take_profit_pct', 'stop_loss_pct'] and value > 100:
                        errors.append(f"{name}不能超过100%")
                except ValueError:
                    errors.append(f"{name}必须是有效数字")
        
        # 验证时间格式
        time_fields = ['entry_time', 'exit_time']
        for field in time_fields:
            if data.get(field):
                try:
                    datetime.strptime(data[field], "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    errors.append(f"{field}时间格式不正确，应为：YYYY-MM-DD HH:MM:SS")
        
        # 验证交易方向
        if data.get('direction') and data['direction'] not in ['多头', '空头']:
            errors.append("交易方向必须是'多头'或'空头'")
        
        return errors
    
    def save_trade(self):
        """保存交易记录"""
        try:
            data = self.view.get_form_data()
            
            # 验证数据
            errors = self.validate_form_data(data)
            if errors:
                error_message = "\n".join(errors)
                self.view.show_message("验证错误", error_message, "error")
                return
            
            # 转换数据类型
            processed_data = {
                'position_size': float(data['position_size']),
                'direction': data['direction'],
                'take_profit_pct': float(data['take_profit_pct']),
                'stop_loss_pct': float(data['stop_loss_pct']),
                'entry_time': data['entry_time'],
                'exit_time': data['exit_time'],
                'entry_fee_pct': float(data['entry_fee_pct']),
                'exit_fee_pct': float(data['exit_fee_pct']),
                'instrument': data.get('instrument', ''),
                'strategy': data.get('strategy', ''),
                'notes': data.get('notes', '')
            }
            
            if self.current_edit_id:
                # 更新现有记录
                self.trade_model.update_trade(self.current_edit_id, processed_data)
                self.view.show_message("成功", "交易记录已更新")
                self.current_edit_id = None
            else:
                # 添加新记录
                self.trade_model.add_trade(processed_data)
                self.view.show_message("成功", "交易记录已保存")
            
            # 清空表单并刷新数据
            self.view.clear_form()
            self.refresh_data()
            self.refresh_analytics()
            self.view.update_status("交易记录已保存")
            
        except Exception as e:
            self.view.show_message("错误", f"保存失败：{str(e)}", "error")
    
    def new_trade(self):
        """新建交易"""
        self.current_edit_id = None
        self.view.clear_form()
        self.view.load_defaults()
        self.view.notebook.select(0)  # 切换到添加交易标签页
        self.view.update_status("准备添加新交易")
    
    def refresh_data(self):
        """刷新交易数据"""
        try:
            trades_df = self.trade_model.get_all_trades()
            self.view.update_trade_list(trades_df)
            self.view.update_status(f"已加载 {len(trades_df)} 条交易记录")
        except Exception as e:
            self.view.show_message("错误", f"刷新数据失败：{str(e)}", "error")
    
    def refresh_analytics(self):
        """刷新统计分析"""
        try:
            trades_df = self.trade_model.get_all_trades()
            stats = TradingCalculator.calculate_statistics(trades_df)
            self.view.update_analytics(stats)
            self.view.update_status("统计数据已更新")
        except Exception as e:
            self.view.show_message("错误", f"刷新统计失败：{str(e)}", "error")
    
    def delete_selected_trade(self):
        """删除选中的交易"""
        trade_id = self.view.get_selected_trade_id()
        if not trade_id:
            self.view.show_message("提示", "请先选择要删除的交易记录", "warning")
            return
        
        # 确认删除
        from tkinter import messagebox
        if messagebox.askyesno("确认删除", "确定要删除选中的交易记录吗？"):
            try:
                self.trade_model.delete_trade(trade_id)
                self.refresh_data()
                self.refresh_analytics()
                self.view.show_message("成功", "交易记录已删除")
                self.view.update_status("交易记录已删除")
            except Exception as e:
                self.view.show_message("错误", f"删除失败：{str(e)}", "error")
    
    def edit_selected_trade(self):
        """编辑选中的交易"""
        trade_id = self.view.get_selected_trade_id()
        if not trade_id:
            self.view.show_message("提示", "请先选择要编辑的交易记录", "warning")
            return
        
        try:
            trade = self.trade_model.get_trade_by_id(trade_id)
            if trade:
                self.current_edit_id = trade_id
                
                # 填充表单
                self.view.position_size_var.set(str(trade['position_size']))
                self.view.direction_var.set(trade['direction'])
                self.view.take_profit_var.set(str(trade['take_profit_pct']))
                self.view.stop_loss_var.set(str(trade['stop_loss_pct']))
                self.view.entry_time_var.set(trade['entry_time'])
                self.view.exit_time_var.set(trade['exit_time'])
                self.view.entry_fee_var.set(str(trade['entry_fee_pct']))
                self.view.exit_fee_var.set(str(trade['exit_fee_pct']))
                self.view.instrument_var.set(trade.get('instrument', ''))
                self.view.strategy_var.set(trade.get('strategy', ''))
                self.view.notes_text.delete(1.0, 'end')
                self.view.notes_text.insert(1.0, trade.get('notes', ''))
                
                # 切换到编辑标签页
                self.view.notebook.select(0)
                self.view.update_status(f"正在编辑交易记录 ID: {trade_id}")
            else:
                self.view.show_message("错误", "找不到指定的交易记录", "error")
        except Exception as e:
            self.view.show_message("错误", f"编辑失败：{str(e)}", "error")
    
    def apply_filter(self):
        """应用日期过滤"""
        try:
            start_date = self.view.start_date_var.get()
            end_date = self.view.end_date_var.get()
            
            if not start_date or not end_date:
                self.view.show_message("提示", "请输入开始日期和结束日期", "warning")
                return
            
            # 验证日期格式
            try:
                datetime.strptime(start_date, "%Y-%m-%d")
                datetime.strptime(end_date, "%Y-%m-%d")
            except ValueError:
                self.view.show_message("错误", "日期格式不正确，应为：YYYY-MM-DD", "error")
                return
            
            trades_df = self.trade_model.get_trades_by_date_range(start_date, end_date)
            self.view.update_trade_list(trades_df)
            self.view.update_status(f"过滤结果：{len(trades_df)} 条记录")
            
        except Exception as e:
            self.view.show_message("错误", f"过滤失败：{str(e)}", "error")
    
    def clear_filter(self):
        """清除过滤"""
        self.view.start_date_var.set("")
        self.view.end_date_var.set("")
        self.refresh_data()
    
    def export_csv(self):
        """导出CSV文件"""
        try:
            trades_df = self.trade_model.get_all_trades()
            if trades_df.empty:
                self.view.show_message("提示", "没有数据可导出", "warning")
                return
            
            # 选择保存位置
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="导出交易记录"
            )
            
            if filename:
                trades_df.to_csv(filename, index=False, encoding='utf-8-sig')
                self.view.show_message("成功", f"数据已导出到：{filename}")
                self.view.update_status(f"已导出 {len(trades_df)} 条记录到 CSV 文件")
                
        except Exception as e:
            self.view.show_message("错误", f"导出失败：{str(e)}", "error")
    
    def save_settings(self):
        """保存设置"""
        try:
            settings_data = self.view.get_settings_data()
            
            # 验证设置数据
            for key, value in settings_data.items():
                if value.strip():
                    try:
                        float(value)
                    except ValueError:
                        self.view.show_message("错误", f"设置值必须是有效数字：{key}", "error")
                        return
            
            # 保存设置
            for key, value in settings_data.items():
                if value.strip():
                    self.settings_model.set_setting(key, value)
            
            self.view.show_message("成功", "设置已保存")
            self.view.update_status("设置已保存")
            
        except Exception as e:
            self.view.show_message("错误", f"保存设置失败：{str(e)}", "error")
    
    def load_settings(self):
        """加载设置"""
        try:
            defaults = self.settings_model.get_default_values()
            self.view.load_settings_to_form(defaults)
            self.view.update_status("设置已加载")
        except Exception as e:
            self.view.show_message("错误", f"加载设置失败：{str(e)}", "error")
    
    def get_default_values(self):
        """获取默认值"""
        return self.settings_model.get_default_values()
