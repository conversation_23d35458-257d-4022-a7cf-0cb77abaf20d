"""
GUI界面模块
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta

class TradingReviewApp:
    def __init__(self, root, controller):
        self.root = root
        self.controller = controller
        self.setup_main_window()
        self.create_widgets()

    def setup_main_window(self):
        """设置主窗口"""
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

    def safe_call(self, method_name):
        """安全调用控制器方法"""
        def wrapper():
            if self.controller and hasattr(self.controller, method_name):
                getattr(self.controller, method_name)()
            else:
                print(f"控制器方法 {method_name} 不可用")
        return wrapper

    def create_widgets(self):
        """创建主要组件"""
        # 创建菜单栏
        self.create_menu()

        # 创建状态栏
        self.status_bar = ttk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 创建主要的标签页
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建各个标签页
        self.create_add_trade_tab()
        self.create_history_tab()
        self.create_analytics_tab()
        self.create_settings_tab()

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建交易 (Ctrl+N)", command=self.safe_call('new_trade'))
        file_menu.add_command(label="保存 (Ctrl+S)", command=self.safe_call('save_trade'))
        file_menu.add_separator()
        file_menu.add_command(label="导出CSV", command=self.safe_call('export_csv'))
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="删除选中交易", command=self.safe_call('delete_selected_trade'))

        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="刷新数据", command=self.safe_call('refresh_data'))

        # 绑定快捷键
        self.root.bind('<Control-n>', lambda e: self.safe_call('new_trade')())
        self.root.bind('<Control-s>', lambda e: self.safe_call('save_trade')())

    def create_add_trade_tab(self):
        """创建添加交易标签页"""
        self.add_trade_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.add_trade_frame, text="添加交易")

        # 创建滚动框架
        canvas = tk.Canvas(self.add_trade_frame)
        scrollbar = ttk.Scrollbar(self.add_trade_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 主要输入区域
        main_frame = ttk.LabelFrame(scrollable_frame, text="交易信息", padding="10")
        main_frame.pack(fill=tk.X, padx=10, pady=5)

        # 必填字段
        row = 0

        # 仓位大小
        ttk.Label(main_frame, text="仓位大小 *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.position_size_var = tk.StringVar()
        self.position_size_entry = ttk.Entry(main_frame, textvariable=self.position_size_var, width=20)
        self.position_size_entry.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        row += 1

        # 交易方向
        ttk.Label(main_frame, text="交易方向 *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.direction_var = tk.StringVar()
        self.direction_combo = ttk.Combobox(main_frame, textvariable=self.direction_var,
                                          values=["多头", "空头"], state="readonly", width=17)
        self.direction_combo.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        row += 1

        # 止盈百分比
        ttk.Label(main_frame, text="止盈百分比 *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.take_profit_var = tk.StringVar()
        self.take_profit_entry = ttk.Entry(main_frame, textvariable=self.take_profit_var, width=20)
        self.take_profit_entry.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(main_frame, text="%").grid(row=row, column=2, sticky=tk.W, padx=(5, 0), pady=2)
        row += 1

        # 止损百分比
        ttk.Label(main_frame, text="止损百分比 *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        self.stop_loss_var = tk.StringVar()
        self.stop_loss_entry = ttk.Entry(main_frame, textvariable=self.stop_loss_var, width=20)
        self.stop_loss_entry.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(main_frame, text="%").grid(row=row, column=2, sticky=tk.W, padx=(5, 0), pady=2)
        row += 1

        # 时间框架
        time_frame = ttk.LabelFrame(scrollable_frame, text="时间信息", padding="10")
        time_frame.pack(fill=tk.X, padx=10, pady=5)

        # 入场时间
        ttk.Label(time_frame, text="入场时间 *:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.entry_time_var = tk.StringVar()
        self.entry_time_entry = ttk.Entry(time_frame, textvariable=self.entry_time_var, width=20)
        self.entry_time_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Button(time_frame, text="现在", command=self.set_current_time_entry).grid(row=0, column=2, padx=(5, 0), pady=2)

        # 出场时间
        ttk.Label(time_frame, text="出场时间 *:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.exit_time_var = tk.StringVar()
        self.exit_time_entry = ttk.Entry(time_frame, textvariable=self.exit_time_var, width=20)
        self.exit_time_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Button(time_frame, text="现在", command=self.set_current_time_exit).grid(row=1, column=2, padx=(5, 0), pady=2)

        # 手续费框架
        fee_frame = ttk.LabelFrame(scrollable_frame, text="手续费信息", padding="10")
        fee_frame.pack(fill=tk.X, padx=10, pady=5)

        # 入场手续费
        ttk.Label(fee_frame, text="入场手续费:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.entry_fee_var = tk.StringVar()
        self.entry_fee_entry = ttk.Entry(fee_frame, textvariable=self.entry_fee_var, width=20)
        self.entry_fee_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(fee_frame, text="%").grid(row=0, column=2, sticky=tk.W, padx=(5, 0), pady=2)

        # 出场手续费
        ttk.Label(fee_frame, text="出场手续费:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.exit_fee_var = tk.StringVar()
        self.exit_fee_entry = ttk.Entry(fee_frame, textvariable=self.exit_fee_var, width=20)
        self.exit_fee_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(fee_frame, text="%").grid(row=1, column=2, sticky=tk.W, padx=(5, 0), pady=2)

        # 可选字段框架
        optional_frame = ttk.LabelFrame(scrollable_frame, text="可选信息", padding="10")
        optional_frame.pack(fill=tk.X, padx=10, pady=5)

        # 交易品种
        ttk.Label(optional_frame, text="交易品种:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.instrument_var = tk.StringVar()
        self.instrument_entry = ttk.Entry(optional_frame, textvariable=self.instrument_var, width=20)
        self.instrument_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 交易策略
        ttk.Label(optional_frame, text="交易策略:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.strategy_var = tk.StringVar()
        self.strategy_entry = ttk.Entry(optional_frame, textvariable=self.strategy_var, width=20)
        self.strategy_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 备注
        ttk.Label(optional_frame, text="备注:").grid(row=2, column=0, sticky=tk.NW, pady=2)
        self.notes_text = tk.Text(optional_frame, width=40, height=4)
        self.notes_text.grid(row=2, column=1, columnspan=2, sticky=tk.W, padx=(10, 0), pady=2)

        # 按钮框架
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="保存交易", command=self.safe_call('save_trade')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空表单", command=self.clear_form).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="加载默认值", command=self.load_defaults).pack(side=tk.LEFT)

        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def set_current_time_entry(self):
        """设置当前时间为入场时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.entry_time_var.set(current_time)

    def set_current_time_exit(self):
        """设置当前时间为出场时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.exit_time_var.set(current_time)

    def clear_form(self):
        """清空表单"""
        self.position_size_var.set("")
        self.direction_var.set("")
        self.take_profit_var.set("")
        self.stop_loss_var.set("")
        self.entry_time_var.set("")
        self.exit_time_var.set("")
        self.entry_fee_var.set("")
        self.exit_fee_var.set("")
        self.instrument_var.set("")
        self.strategy_var.set("")
        self.notes_text.delete(1.0, tk.END)

    def load_defaults(self):
        """加载默认值"""
        if self.controller:
            defaults = self.controller.get_default_values()
            self.position_size_var.set(str(defaults['position_size']))
            self.entry_fee_var.set(str(defaults['entry_fee']))
            self.exit_fee_var.set(str(defaults['exit_fee']))
            self.take_profit_var.set(str(defaults['take_profit']))
            self.stop_loss_var.set(str(defaults['stop_loss']))

    def get_form_data(self):
        """获取表单数据"""
        return {
            'position_size': self.position_size_var.get(),
            'direction': self.direction_var.get(),
            'take_profit_pct': self.take_profit_var.get(),
            'stop_loss_pct': self.stop_loss_var.get(),
            'entry_time': self.entry_time_var.get(),
            'exit_time': self.exit_time_var.get(),
            'entry_fee_pct': self.entry_fee_var.get(),
            'exit_fee_pct': self.exit_fee_var.get(),
            'instrument': self.instrument_var.get(),
            'strategy': self.strategy_var.get(),
            'notes': self.notes_text.get(1.0, tk.END).strip()
        }

    def create_history_tab(self):
        """创建交易历史标签页"""
        self.history_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.history_frame, text="交易历史")

        # 过滤器框架
        filter_frame = ttk.LabelFrame(self.history_frame, text="过滤器", padding="10")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)

        # 日期过滤
        ttk.Label(filter_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.start_date_var = tk.StringVar()
        self.start_date_entry = ttk.Entry(filter_frame, textvariable=self.start_date_var, width=15)
        self.start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(filter_frame, text="结束日期:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=2)
        self.end_date_var = tk.StringVar()
        self.end_date_entry = ttk.Entry(filter_frame, textvariable=self.end_date_var, width=15)
        self.end_date_entry.grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Button(filter_frame, text="应用过滤", command=self.safe_call('apply_filter')).grid(row=0, column=4, padx=(20, 0), pady=2)
        ttk.Button(filter_frame, text="清除过滤", command=self.safe_call('clear_filter')).grid(row=0, column=5, padx=(10, 0), pady=2)

        # 交易列表
        list_frame = ttk.Frame(self.history_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建Treeview
        columns = ('ID', '仓位大小', '方向', '止盈%', '止损%', '入场时间', '出场时间', '品种', '策略')
        self.trade_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.trade_tree.heading(col, text=col)
            self.trade_tree.column(col, width=100)

        # 滚动条
        tree_scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.trade_tree.yview)
        tree_scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.trade_tree.xview)
        self.trade_tree.configure(yscrollcommand=tree_scrollbar_y.set, xscrollcommand=tree_scrollbar_x.set)

        # 布局
        self.trade_tree.grid(row=0, column=0, sticky='nsew')
        tree_scrollbar_y.grid(row=0, column=1, sticky='ns')
        tree_scrollbar_x.grid(row=1, column=0, sticky='ew')

        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)

        # 操作按钮
        button_frame = ttk.Frame(self.history_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="编辑选中", command=self.safe_call('edit_selected_trade')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="删除选中", command=self.safe_call('delete_selected_trade')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="刷新", command=self.safe_call('refresh_data')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出CSV", command=self.safe_call('export_csv')).pack(side=tk.RIGHT)

    def create_analytics_tab(self):
        """创建分析统计标签页"""
        self.analytics_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analytics_frame, text="统计分析")

        # 创建滚动框架
        canvas = tk.Canvas(self.analytics_frame)
        scrollbar = ttk.Scrollbar(self.analytics_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 统计信息框架
        stats_frame = ttk.LabelFrame(scrollable_frame, text="交易统计", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建统计标签
        self.stats_labels = {}
        stats_items = [
            ('total_trades', '总交易次数'),
            ('win_rate', '胜率 (%)'),
            ('total_pnl', '总盈亏'),
            ('avg_profit', '平均盈利'),
            ('avg_loss', '平均亏损'),
            ('profit_factor', '盈利因子'),
            ('total_fees', '总手续费'),
            ('max_drawdown', '最大回撤'),
            ('max_consecutive_wins', '最大连胜'),
            ('max_consecutive_losses', '最大连败')
        ]

        row = 0
        col = 0
        for key, label in stats_items:
            ttk.Label(stats_frame, text=f"{label}:").grid(row=row, column=col*2, sticky=tk.W, pady=2, padx=(0, 10))
            self.stats_labels[key] = ttk.Label(stats_frame, text="0", font=('Arial', 10, 'bold'))
            self.stats_labels[key].grid(row=row, column=col*2+1, sticky=tk.W, pady=2, padx=(0, 30))

            col += 1
            if col >= 2:
                col = 0
                row += 1

        # 刷新按钮
        ttk.Button(stats_frame, text="刷新统计", command=self.safe_call('refresh_analytics')).pack(pady=10)

        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_settings_tab(self):
        """创建设置标签页"""
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="设置")

        # 默认值设置框架
        defaults_frame = ttk.LabelFrame(self.settings_frame, text="默认值设置", padding="10")
        defaults_frame.pack(fill=tk.X, padx=10, pady=10)

        # 默认仓位大小
        ttk.Label(defaults_frame, text="默认仓位大小:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.default_position_var = tk.StringVar()
        ttk.Entry(defaults_frame, textvariable=self.default_position_var, width=20).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 默认入场手续费
        ttk.Label(defaults_frame, text="默认入场手续费 (%):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.default_entry_fee_var = tk.StringVar()
        ttk.Entry(defaults_frame, textvariable=self.default_entry_fee_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 默认出场手续费
        ttk.Label(defaults_frame, text="默认出场手续费 (%):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.default_exit_fee_var = tk.StringVar()
        ttk.Entry(defaults_frame, textvariable=self.default_exit_fee_var, width=20).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 默认止盈
        ttk.Label(defaults_frame, text="默认止盈 (%):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.default_take_profit_var = tk.StringVar()
        ttk.Entry(defaults_frame, textvariable=self.default_take_profit_var, width=20).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 默认止损
        ttk.Label(defaults_frame, text="默认止损 (%):").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.default_stop_loss_var = tk.StringVar()
        ttk.Entry(defaults_frame, textvariable=self.default_stop_loss_var, width=20).grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 按钮
        button_frame = ttk.Frame(defaults_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="保存设置", command=self.safe_call('save_settings')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="加载设置", command=self.safe_call('load_settings')).pack(side=tk.LEFT)

    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)

    def show_message(self, title, message, msg_type="info"):
        """显示消息框"""
        if msg_type == "error":
            messagebox.showerror(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        else:
            messagebox.showinfo(title, message)

    def update_trade_list(self, trades_df):
        """更新交易列表"""
        # 清空现有数据
        for item in self.trade_tree.get_children():
            self.trade_tree.delete(item)

        # 添加新数据
        if not trades_df.empty:
            for _, trade in trades_df.iterrows():
                values = (
                    trade['id'],
                    trade['position_size'],
                    trade['direction'],
                    f"{trade['take_profit_pct']}%",
                    f"{trade['stop_loss_pct']}%",
                    trade['entry_time'],
                    trade['exit_time'],
                    trade.get('instrument', ''),
                    trade.get('strategy', '')
                )
                self.trade_tree.insert('', 'end', values=values)

    def update_analytics(self, stats):
        """更新统计数据"""
        for key, value in stats.items():
            if key in self.stats_labels:
                if key == 'win_rate':
                    self.stats_labels[key].config(text=f"{value}%")
                elif key in ['total_pnl', 'avg_profit', 'avg_loss', 'total_fees', 'max_drawdown']:
                    self.stats_labels[key].config(text=f"¥{value:,.2f}")
                else:
                    self.stats_labels[key].config(text=str(value))

    def get_selected_trade_id(self):
        """获取选中的交易ID"""
        selection = self.trade_tree.selection()
        if selection:
            item = self.trade_tree.item(selection[0])
            return item['values'][0]  # ID是第一列
        return None

    def load_settings_to_form(self, settings):
        """将设置加载到表单"""
        self.default_position_var.set(str(settings.get('position_size', 1000)))
        self.default_entry_fee_var.set(str(settings.get('entry_fee', 0.1)))
        self.default_exit_fee_var.set(str(settings.get('exit_fee', 0.1)))
        self.default_take_profit_var.set(str(settings.get('take_profit', 2.0)))
        self.default_stop_loss_var.set(str(settings.get('stop_loss', 1.0)))

    def get_settings_data(self):
        """获取设置数据"""
        return {
            'default_position_size': self.default_position_var.get(),
            'default_entry_fee': self.default_entry_fee_var.get(),
            'default_exit_fee': self.default_exit_fee_var.get(),
            'default_take_profit': self.default_take_profit_var.get(),
            'default_stop_loss': self.default_stop_loss_var.get()
        }
