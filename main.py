#!/usr/bin/env python3
"""
交易复盘工具 - 主程序入口
"""
import tkinter as tk
import tkinter.messagebox as messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from view import TradingReviewApp
from controller import TradingController

def main():
    """主函数"""
    print("开始启动交易复盘工具...")
    try:
        # 在macOS上设置正确的显示环境
        import os
        if os.name == 'posix':  # Unix/Linux/macOS
            os.environ['TK_SILENCE_DEPRECATION'] = '1'

        print("环境设置完成...")

        # 创建主窗口
        print("创建主窗口...")
        root = tk.Tk()

        # 设置窗口属性
        print("设置窗口属性...")
        root.title("交易复盘工具")
        root.geometry("1200x800")
        root.minsize(1000, 600)

        print("窗口设置完成...")

        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置应用程序图标
            # root.iconbitmap('icon.ico')
            pass
        except:
            pass

        # 创建控制器和视图
        print("创建应用程序组件...")
        controller = None
        app_view = TradingReviewApp(root, controller)
        print("视图创建完成...")
        controller = TradingController(app_view)
        print("控制器创建完成...")
        app_view.controller = controller

        # 设置窗口关闭事件
        def on_closing():
            """窗口关闭时的处理"""
            try:
                if messagebox.askokcancel("退出", "确定要退出交易复盘工具吗？"):
                    root.destroy()
            except:
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动应用程序
        print("交易复盘工具启动中...")
        print("如果窗口没有显示，请检查Dock或任务栏中的Python图标")

        # 简单的窗口显示设置
        try:
            root.lift()
            root.focus_force()
        except:
            pass

        print("开始主循环...")
        root.mainloop()

    except Exception as e:
        print(f"应用程序启动失败：{e}")
        import traceback
        traceback.print_exc()

        # 显示错误对话框
        try:
            messagebox.showerror("启动错误", f"应用程序启动失败：\n{e}")
        except:
            pass

if __name__ == "__main__":
    main()
