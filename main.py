#!/usr/bin/env python3
"""
交易复盘工具 - 主程序入口
"""
import tkinter as tk
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from view import TradingReviewApp
from controller import TradingController

def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置应用程序图标
            # root.iconbitmap('icon.ico')
            pass
        except:
            pass

        # 创建控制器和视图
        controller = None
        app_view = TradingReviewApp(root, controller)
        controller = TradingController(app_view)
        app_view.controller = controller

        # 设置窗口关闭事件
        def on_closing():
            """窗口关闭时的处理"""
            if tk.messagebox.askokcancel("退出", "确定要退出交易复盘工具吗？"):
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动应用程序
        print("交易复盘工具启动中...")
        root.mainloop()

    except Exception as e:
        print(f"应用程序启动失败：{e}")
        import traceback
        traceback.print_exc()

        # 显示错误对话框
        try:
            import tkinter.messagebox as messagebox
            messagebox.showerror("启动错误", f"应用程序启动失败：\n{e}")
        except:
            pass

if __name__ == "__main__":
    main()
