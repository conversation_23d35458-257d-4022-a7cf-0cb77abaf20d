"""
交易指标计算模块
"""

class TradingCalculator:
    @staticmethod
    def calculate_trade_metrics(position_size, direction, take_profit_pct, stop_loss_pct,
                              entry_fee_pct, exit_fee_pct, is_win=True):
        """
        计算单笔交易指标

        Args:
            position_size: 仓位大小
            direction: 交易方向 ('多头' 或 '空头')
            take_profit_pct: 止盈百分比
            stop_loss_pct: 止损百分比
            entry_fee_pct: 入场手续费百分比
            exit_fee_pct: 出场手续费百分比
            is_win: 是否盈利

        Returns:
            dict: 包含各项计算结果的字典
        """
        # 计算手续费
        entry_fee = position_size * (entry_fee_pct / 100)
        exit_fee = position_size * (exit_fee_pct / 100)
        total_fees = entry_fee + exit_fee

        # 计算盈亏
        if is_win:
            profit_loss_pct = take_profit_pct
        else:
            profit_loss_pct = -stop_loss_pct

        # 根据方向调整盈亏
        if direction == '空头':
            profit_loss_pct = -profit_loss_pct

        profit_loss_amount = position_size * (profit_loss_pct / 100)
        net_profit_loss = profit_loss_amount - total_fees

        # 计算风险回报比
        risk_reward_ratio = take_profit_pct / stop_loss_pct if stop_loss_pct > 0 else 0

        return {
            'profit_loss_amount': profit_loss_amount,
            'profit_loss_pct': profit_loss_pct,
            'total_fees': total_fees,
            'net_profit_loss': net_profit_loss,
            'risk_reward_ratio': risk_reward_ratio
        }

    @staticmethod
    def calculate_statistics(trades_df):
        """
        计算交易统计数据

        Args:
            trades_df: 包含交易数据的DataFrame

        Returns:
            dict: 统计结果
        """
        if trades_df.empty:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_pnl': 0,
                'avg_profit': 0,
                'avg_loss': 0,
                'profit_factor': 0,
                'total_fees': 0,
                'max_drawdown': 0,
                'max_consecutive_wins': 0,
                'max_consecutive_losses': 0
            }

        # 基本统计
        total_trades = len(trades_df)

        # 计算每笔交易的盈亏
        trades_with_pnl = []
        for _, trade in trades_df.iterrows():
            # 简化处理：假设随机盈亏，实际应用中需要根据实际结果判断
            # 这里可以根据实际需求添加盈亏判断逻辑
            import random
            is_win = random.choice([True, False])  # 临时随机，实际应根据交易结果

            metrics = TradingCalculator.calculate_trade_metrics(
                trade['position_size'],
                trade['direction'],
                trade['take_profit_pct'],
                trade['stop_loss_pct'],
                trade['entry_fee_pct'],
                trade['exit_fee_pct'],
                is_win
            )
            trades_with_pnl.append(metrics['net_profit_loss'])

        trades_df['net_pnl'] = trades_with_pnl

        # 胜率计算
        winning_trades = trades_df[trades_df['net_pnl'] > 0]
        losing_trades = trades_df[trades_df['net_pnl'] < 0]

        win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0

        # 平均盈利和亏损
        avg_profit = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(losing_trades['net_pnl'].mean()) if len(losing_trades) > 0 else 0

        # 盈利因子
        gross_profit = winning_trades['net_pnl'].sum() if len(winning_trades) > 0 else 0
        gross_loss = abs(losing_trades['net_pnl'].sum()) if len(losing_trades) > 0 else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # 总盈亏
        total_pnl = trades_df['net_pnl'].sum()

        # 最大回撤
        cumulative_pnl = trades_df['net_pnl'].cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = (cumulative_pnl - running_max)
        max_drawdown = abs(drawdown.min()) if len(drawdown) > 0 else 0

        # 连续盈亏
        consecutive_wins = TradingCalculator._calculate_consecutive(trades_df['net_pnl'] > 0)
        consecutive_losses = TradingCalculator._calculate_consecutive(trades_df['net_pnl'] < 0)

        return {
            'total_trades': total_trades,
            'win_rate': round(win_rate, 2),
            'total_pnl': round(total_pnl, 2),
            'avg_profit': round(avg_profit, 2),
            'avg_loss': round(avg_loss, 2),
            'profit_factor': round(profit_factor, 2),
            'total_fees': round(trades_df['entry_fee_pct'].sum() + trades_df['exit_fee_pct'].sum(), 2),
            'max_drawdown': round(max_drawdown, 2),
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses
        }

    @staticmethod
    def _calculate_consecutive(boolean_series):
        """计算连续True的最大长度"""
        if boolean_series.empty:
            return 0

        max_consecutive = 0
        current_consecutive = 0

        for value in boolean_series:
            if value:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive
