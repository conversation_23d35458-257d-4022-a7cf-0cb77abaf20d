#!/usr/bin/env python3
"""
简单的GUI测试程序
"""
import tkinter as tk
from tkinter import ttk
import os

def main():
    print("开始创建简单GUI测试...")
    
    # 设置环境
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    # 创建主窗口
    root = tk.Tk()
    root.title("简单GUI测试")
    root.geometry("800x600")
    
    print("主窗口创建完成")
    
    # 创建一个简单的标签
    label = tk.Label(root, text="这是一个测试标签", font=("Arial", 16))
    label.pack(pady=20)
    
    print("标签创建完成")
    
    # 创建一个按钮
    def button_click():
        print("按钮被点击了！")
        label.config(text="按钮被点击了！")
    
    button = tk.Button(root, text="点击我", command=button_click, font=("Arial", 12))
    button.pack(pady=10)
    
    print("按钮创建完成")
    
    # 创建一个简单的Notebook
    notebook = ttk.Notebook(root)
    
    # 第一个标签页
    frame1 = ttk.Frame(notebook)
    notebook.add(frame1, text="标签页1")
    
    label1 = tk.Label(frame1, text="这是第一个标签页", font=("Arial", 14))
    label1.pack(pady=20)
    
    # 第二个标签页
    frame2 = ttk.Frame(notebook)
    notebook.add(frame2, text="标签页2")
    
    label2 = tk.Label(frame2, text="这是第二个标签页", font=("Arial", 14))
    label2.pack(pady=20)
    
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    print("Notebook创建完成")
    
    # 强制更新显示
    root.update_idletasks()
    root.lift()
    root.focus_force()
    
    print("GUI测试程序启动完成，开始主循环...")
    
    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
