Create a comprehensive trading review tool application with the following detailed specifications:

## Project Overview
Develop a local desktop trading review application to help traders record, analyze, and optimize their trading performance. The tool should provide an intuitive interface for data entry, automatic calculation of key trading metrics, and clear visualization of trading performance.

## Core Requirements

### 1. Data Input Module
**Required Fields:**
- Position size (numeric input with validation)
- Trade direction (dropdown: Long/Short)
- Take profit percentage (percentage input, 0-100%)
- Stop loss percentage (percentage input, 0-100%)
- Entry time (datetime picker with timezone support)
- Exit time (datetime picker with timezone support)
- Entry fee percentage (percentage input, default 0.1%)
- Exit fee percentage (percentage input, default 0.1%)

**Optional Fields:**
- Trading instrument/symbol (text input)
- Trading strategy/reason (text area)
- Notes (text area)

**Default Value System:**
- All fields must support user-customizable default values
- Quick-fill buttons for common configurations
- Settings persistence across sessions

### 2. Calculation Engine
**Per-Trade Calculations:**
- Profit/Loss amount and percentage
- Total fees
- Net profit/loss
- Risk-reward ratio

**Statistical Analysis:**
- Daily win rate
- Cumulative P&L
- Average profit and average loss
- Profit factor (gross profit / gross loss)
- Total fees paid
- Maximum drawdown
- Number of consecutive wins/losses

### 3. Data Management
- CRUD operations for trading records
- Advanced filtering by date range, direction, P&L, instrument
- Data export to CSV format
- Data backup and restore functionality

## Technical Implementation

### Technology Stack
- **Language:** Python 3.8+
- **GUI Framework:** Tkinter (built-in, cross-platform)
- **Database:** SQLite with proper schema design
- **Additional Libraries:** 
  - pandas for data manipulation
  - datetime for time handling
  - csv for export functionality

### Architecture
Use MVC (Model-View-Controller) pattern:
- `model.py`: Database operations and data structures
- `view.py`: GUI components and layout
- `controller.py`: Business logic and event handling
- `calculator.py`: Trading metrics calculations
- `database.py`: Database schema and connection management

### Database Schema
```sql
-- Trading records table
CREATE TABLE trades (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    position_size REAL NOT NULL,
    direction TEXT NOT NULL CHECK(direction IN ('Long', 'Short')),
    take_profit_pct REAL NOT NULL,
    stop_loss_pct REAL NOT NULL,
    entry_time DATETIME NOT NULL,
    exit_time DATETIME NOT NULL,
    entry_fee_pct REAL NOT NULL,
    exit_fee_pct REAL NOT NULL,
    instrument TEXT,
    strategy TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- User settings table
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL
);
```

### UI Design Requirements
**Main Window Layout:**
- Tabbed interface with 4 main sections:
  1. "Add Trade" - Data input form
  2. "Trade History" - Searchable/filterable record list
  3. "Analytics" - Statistical dashboard
  4. "Settings" - Configuration panel

**Form Validation:**
- Real-time input validation with error highlighting
- Prevent submission of invalid data
- Clear error messages

**User Experience:**
- Keyboard shortcuts for common actions (Ctrl+N for new trade, Ctrl+S for save)
- Status bar for operation feedback
- Confirmation dialogs for destructive actions
- Auto-save functionality

## Development Phases
1. **Setup Phase:** Project structure, virtual environment, dependencies
2. **Core Development:** Database setup, basic CRUD operations, simple UI
3. **Calculation Engine:** Implement all trading metrics and statistics
4. **UI Polish:** Enhanced interface, validation, user experience improvements
5. **Advanced Features:** Export functionality, settings system, data visualization

## Deliverables
- Complete Python application with executable
- SQLite database with sample data
- User documentation/README
- Source code with proper commenting
- Requirements.txt for dependencies

Build this as a production-ready desktop application that traders can use daily for performance analysis.