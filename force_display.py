#!/usr/bin/env python3
"""
强制显示GUI窗口的测试程序
"""
import tkinter as tk
from tkinter import ttk
import os
import subprocess
import sys

def force_display_window():
    """强制显示窗口"""
    # 设置环境变量
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    # 创建根窗口
    root = tk.Tk()
    root.title("🚀 强制显示测试")
    root.geometry("500x300")
    
    # 多种方法确保窗口显示
    root.lift()
    root.attributes('-topmost', True)
    root.focus_force()
    root.grab_set()
    
    # 在macOS上尝试激活应用程序
    if sys.platform == 'darwin':
        try:
            # 尝试使用osascript激活窗口
            subprocess.run(['osascript', '-e', 'tell application "Python" to activate'], 
                         capture_output=True, timeout=2)
        except:
            pass
    
    # 创建内容
    frame = ttk.Frame(root, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)
    
    # 大标题
    title = ttk.Label(frame, text="🎯 GUI测试成功！", font=('Arial', 18, 'bold'))
    title.pack(pady=20)
    
    # 说明文本
    text = ttk.Label(frame, text="如果您能看到这个窗口，说明GUI正常工作！\n\n现在可以运行完整的交易复盘工具了。", 
                    font=('Arial', 12), justify=tk.CENTER)
    text.pack(pady=20)
    
    # 按钮
    def close_and_launch():
        root.destroy()
        print("\n" + "="*50)
        print("GUI测试成功！现在启动完整应用程序...")
        print("="*50)
        # 启动主应用程序
        os.system("python3 main.py &")
    
    ttk.Button(frame, text="启动完整应用程序", command=close_and_launch).pack(pady=10)
    ttk.Button(frame, text="关闭", command=root.quit).pack(pady=5)
    
    # 延迟移除topmost属性
    root.after(3000, lambda: root.attributes('-topmost', False))
    
    print("\n" + "="*60)
    print("🚀 强制显示测试程序已启动")
    print("="*60)
    print("正在尝试多种方法显示窗口...")
    print("请查看屏幕上是否出现了GUI窗口")
    print("="*60)
    
    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    force_display_window()
