#!/usr/bin/env python3
import tkinter as tk
import os

# 设置环境变量
os.environ['TK_SILENCE_DEPRECATION'] = '1'

print("创建基础窗口...")
root = tk.Tk()
root.title("基础测试")
root.geometry("400x300")

# 设置背景色以便看到窗口
root.configure(bg='lightblue')

print("添加文本...")
label = tk.Label(root, text="Hello World!", font=("Arial", 20), bg='white')
label.place(x=100, y=100)

print("强制更新...")
root.update()
root.lift()
root.attributes('-topmost', True)
root.after(1000, lambda: root.attributes('-topmost', False))

print("启动主循环...")
root.mainloop()
