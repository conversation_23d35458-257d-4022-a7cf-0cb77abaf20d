#!/usr/bin/env python3
"""
测试导入模块
"""
print("开始测试导入...")

try:
    import tkinter as tk
    print("✓ tkinter 导入成功")
except Exception as e:
    print(f"✗ tkinter 导入失败: {e}")

try:
    import sys
    import os
    print("✓ sys, os 导入成功")
except Exception as e:
    print(f"✗ sys, os 导入失败: {e}")

try:
    from view import TradingReviewApp
    print("✓ view 模块导入成功")
except Exception as e:
    print(f"✗ view 模块导入失败: {e}")

try:
    from controller import TradingController
    print("✓ controller 模块导入成功")
except Exception as e:
    print(f"✗ controller 模块导入失败: {e}")

try:
    from model import TradeModel, SettingsModel
    print("✓ model 模块导入成功")
except Exception as e:
    print(f"✗ model 模块导入失败: {e}")

try:
    from calculator import TradingCalculator
    print("✓ calculator 模块导入成功")
except Exception as e:
    print(f"✗ calculator 模块导入失败: {e}")

print("导入测试完成")
