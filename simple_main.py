#!/usr/bin/env python3
"""
简化的交易复盘工具 - 主程序入口
"""
import tkinter as tk
import os
from simple_view import SimpleTradingApp

def main():
    """主函数"""
    print("开始启动简化的交易复盘工具...")
    try:
        # 在macOS上设置正确的显示环境
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        print("环境设置完成...")

        # 创建主窗口
        print("创建主窗口...")
        root = tk.Tk()
        
        # 设置窗口属性
        print("设置窗口属性...")
        root.title("交易复盘工具 - 简化版")
        root.geometry("800x600")
        root.minsize(600, 400)
        
        print("窗口设置完成...")

        # 创建应用程序
        print("创建应用程序组件...")
        app = SimpleTradingApp(root)
        
        print("应用程序创建完成...")

        # 启动应用程序
        print("交易复盘工具启动中...")
        
        # 简单的窗口显示设置
        try:
            root.lift()
            root.focus_force()
        except:
            pass
        
        print("开始主循环...")
        root.mainloop()

    except Exception as e:
        print(f"应用程序启动失败：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
