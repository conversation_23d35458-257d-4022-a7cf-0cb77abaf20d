#!/usr/bin/env python3
"""
工作版本的交易复盘工具
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime

class WorkingTradingApp:
    def __init__(self):
        # 设置环境
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("交易复盘工具")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 初始化数据库
        self.init_database()
        
        # 创建界面
        self.create_interface()
        
    def init_database(self):
        """初始化数据库"""
        self.conn = sqlite3.connect('trading_review.db')
        cursor = self.conn.cursor()
        
        # 创建交易表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_size REAL NOT NULL,
                direction TEXT NOT NULL,
                take_profit_pct REAL NOT NULL,
                stop_loss_pct REAL NOT NULL,
                entry_time TEXT NOT NULL,
                exit_time TEXT NOT NULL,
                entry_fee_pct REAL DEFAULT 0.1,
                exit_fee_pct REAL DEFAULT 0.1,
                instrument TEXT,
                strategy TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.conn.commit()
        
    def create_interface(self):
        """创建界面"""
        # 创建菜单栏
        self.create_menu()
        
        # 创建主要内容区域
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_add_trade_tab()
        self.create_history_tab()
        self.create_analytics_tab()
        self.create_settings_tab()
        
        # 创建状态栏
        self.status_bar = tk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建交易", command=self.new_trade)
        file_menu.add_command(label="导出数据", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="清空表单", command=self.clear_form)
        edit_menu.add_command(label="刷新数据", command=self.refresh_data)
        
    def create_add_trade_tab(self):
        """创建添加交易标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="添加交易")
        
        # 主容器
        container = tk.Frame(frame)
        container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(container, text="添加新交易", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 输入表单
        form_frame = tk.Frame(container)
        form_frame.pack(anchor=tk.W)
        
        # 仓位大小
        tk.Label(form_frame, text="仓位大小:", font=("Arial", 12)).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.position_var = tk.StringVar()
        tk.Entry(form_frame, textvariable=self.position_var, font=("Arial", 12), width=20).grid(row=0, column=1, padx=10, pady=5)
        
        # 交易方向
        tk.Label(form_frame, text="交易方向:", font=("Arial", 12)).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.direction_var = tk.StringVar()
        direction_combo = ttk.Combobox(form_frame, textvariable=self.direction_var, 
                                     values=["多头", "空头"], state="readonly", width=17)
        direction_combo.grid(row=1, column=1, padx=10, pady=5)
        
        # 止盈百分比
        tk.Label(form_frame, text="止盈百分比:", font=("Arial", 12)).grid(row=2, column=0, sticky=tk.W, pady=5)
        self.take_profit_var = tk.StringVar()
        tk.Entry(form_frame, textvariable=self.take_profit_var, font=("Arial", 12), width=20).grid(row=2, column=1, padx=10, pady=5)
        
        # 止损百分比
        tk.Label(form_frame, text="止损百分比:", font=("Arial", 12)).grid(row=3, column=0, sticky=tk.W, pady=5)
        self.stop_loss_var = tk.StringVar()
        tk.Entry(form_frame, textvariable=self.stop_loss_var, font=("Arial", 12), width=20).grid(row=3, column=1, padx=10, pady=5)
        
        # 入场时间
        tk.Label(form_frame, text="入场时间:", font=("Arial", 12)).grid(row=4, column=0, sticky=tk.W, pady=5)
        self.entry_time_var = tk.StringVar()
        tk.Entry(form_frame, textvariable=self.entry_time_var, font=("Arial", 12), width=20).grid(row=4, column=1, padx=10, pady=5)
        tk.Button(form_frame, text="现在", command=self.set_entry_time_now).grid(row=4, column=2, padx=5, pady=5)
        
        # 出场时间
        tk.Label(form_frame, text="出场时间:", font=("Arial", 12)).grid(row=5, column=0, sticky=tk.W, pady=5)
        self.exit_time_var = tk.StringVar()
        tk.Entry(form_frame, textvariable=self.exit_time_var, font=("Arial", 12), width=20).grid(row=5, column=1, padx=10, pady=5)
        tk.Button(form_frame, text="现在", command=self.set_exit_time_now).grid(row=5, column=2, padx=5, pady=5)
        
        # 按钮区域
        button_frame = tk.Frame(container)
        button_frame.pack(pady=20)
        
        tk.Button(button_frame, text="保存交易", font=("Arial", 12), 
                 bg="lightgreen", command=self.save_trade).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="清空表单", font=("Arial", 12), 
                 bg="lightcoral", command=self.clear_form).pack(side=tk.LEFT, padx=10)
        
    def create_history_tab(self):
        """创建交易历史标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="交易历史")
        
        # 标题
        title_label = tk.Label(frame, text="交易历史记录", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 列表区域
        list_frame = tk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建Treeview
        columns = ('ID', '仓位大小', '方向', '止盈%', '止损%', '入场时间', '出场时间')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮区域
        button_frame = tk.Frame(frame)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="刷新", command=self.refresh_data).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="删除选中", command=self.delete_selected).pack(side=tk.LEFT, padx=5)
        
    def create_analytics_tab(self):
        """创建统计分析标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="统计分析")
        
        # 标题
        title_label = tk.Label(frame, text="交易统计分析", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 统计信息
        stats_frame = tk.Frame(frame)
        stats_frame.pack(pady=20)
        
        self.stats_label = tk.Label(stats_frame, text="暂无统计数据", font=("Arial", 12))
        self.stats_label.pack()
        
        # 刷新按钮
        tk.Button(frame, text="刷新统计", command=self.refresh_analytics).pack(pady=10)
        
    def create_settings_tab(self):
        """创建设置标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="设置")
        
        # 标题
        title_label = tk.Label(frame, text="应用设置", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 设置内容
        settings_frame = tk.Frame(frame)
        settings_frame.pack(pady=20)
        
        tk.Label(settings_frame, text="默认设置将在后续版本中添加", font=("Arial", 12)).pack()
        
    def set_entry_time_now(self):
        """设置入场时间为当前时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.entry_time_var.set(current_time)
        
    def set_exit_time_now(self):
        """设置出场时间为当前时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.exit_time_var.set(current_time)
        
    def save_trade(self):
        """保存交易"""
        try:
            # 获取表单数据
            position = float(self.position_var.get())
            direction = self.direction_var.get()
            take_profit = float(self.take_profit_var.get())
            stop_loss = float(self.stop_loss_var.get())
            entry_time = self.entry_time_var.get()
            exit_time = self.exit_time_var.get()
            
            if not all([position, direction, take_profit, stop_loss, entry_time, exit_time]):
                messagebox.showerror("错误", "请填写所有必填字段")
                return
            
            # 保存到数据库
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO trades (position_size, direction, take_profit_pct, stop_loss_pct, entry_time, exit_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (position, direction, take_profit, stop_loss, entry_time, exit_time))
            
            self.conn.commit()
            
            messagebox.showinfo("成功", "交易已保存")
            self.clear_form()
            self.refresh_data()
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")
            
    def clear_form(self):
        """清空表单"""
        self.position_var.set("")
        self.direction_var.set("")
        self.take_profit_var.set("")
        self.stop_loss_var.set("")
        self.entry_time_var.set("")
        self.exit_time_var.set("")
        
    def refresh_data(self):
        """刷新数据"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 从数据库加载数据
        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM trades ORDER BY created_at DESC')
        trades = cursor.fetchall()
        
        for trade in trades:
            self.tree.insert('', 'end', values=trade[:7])  # 只显示前7列
            
        self.status_bar.config(text=f"已加载 {len(trades)} 条交易记录")
        
    def delete_selected(self):
        """删除选中的交易"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的交易")
            return
            
        if messagebox.askyesno("确认", "确定要删除选中的交易吗？"):
            item = self.tree.item(selection[0])
            trade_id = item['values'][0]
            
            cursor = self.conn.cursor()
            cursor.execute('DELETE FROM trades WHERE id = ?', (trade_id,))
            self.conn.commit()
            
            self.refresh_data()
            
    def refresh_analytics(self):
        """刷新统计数据"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM trades')
        total_trades = cursor.fetchone()[0]
        
        self.stats_label.config(text=f"总交易次数: {total_trades}")
        
    def new_trade(self):
        """新建交易"""
        self.notebook.select(0)  # 切换到添加交易标签页
        self.clear_form()
        
    def export_data(self):
        """导出数据"""
        messagebox.showinfo("提示", "导出功能将在后续版本中添加")
        
    def run(self):
        """运行应用程序"""
        print("启动工作版本交易复盘工具...")
        self.refresh_data()  # 初始加载数据
        self.root.mainloop()
        
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    app = WorkingTradingApp()
    app.run()

if __name__ == "__main__":
    main()
