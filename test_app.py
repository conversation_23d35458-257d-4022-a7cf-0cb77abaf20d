#!/usr/bin/env python3
"""
交易复盘工具测试脚本
"""
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from model import TradeModel, SettingsModel
from calculator import TradingCalculator

def test_database():
    """测试数据库功能"""
    print("测试数据库功能...")
    
    # 测试交易模型
    trade_model = TradeModel()
    
    # 添加测试交易
    test_trade = {
        'position_size': 1000.0,
        'direction': '多头',
        'take_profit_pct': 2.0,
        'stop_loss_pct': 1.0,
        'entry_time': '2024-01-01 10:00:00',
        'exit_time': '2024-01-01 11:00:00',
        'entry_fee_pct': 0.1,
        'exit_fee_pct': 0.1,
        'instrument': 'BTC/USDT',
        'strategy': '突破策略',
        'notes': '测试交易'
    }
    
    trade_id = trade_model.add_trade(test_trade)
    print(f"添加交易成功，ID: {trade_id}")
    
    # 获取所有交易
    trades_df = trade_model.get_all_trades()
    print(f"总交易数量: {len(trades_df)}")
    
    # 测试设置模型
    settings_model = SettingsModel()
    defaults = settings_model.get_default_values()
    print(f"默认设置: {defaults}")
    
    print("数据库测试完成！\n")

def test_calculator():
    """测试计算器功能"""
    print("测试计算器功能...")
    
    # 测试单笔交易计算
    metrics = TradingCalculator.calculate_trade_metrics(
        position_size=1000,
        direction='多头',
        take_profit_pct=2.0,
        stop_loss_pct=1.0,
        entry_fee_pct=0.1,
        exit_fee_pct=0.1,
        is_win=True
    )
    
    print("盈利交易指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value}")
    
    # 测试亏损交易
    metrics_loss = TradingCalculator.calculate_trade_metrics(
        position_size=1000,
        direction='多头',
        take_profit_pct=2.0,
        stop_loss_pct=1.0,
        entry_fee_pct=0.1,
        exit_fee_pct=0.1,
        is_win=False
    )
    
    print("\n亏损交易指标:")
    for key, value in metrics_loss.items():
        print(f"  {key}: {value}")
    
    print("计算器测试完成！\n")

def test_statistics():
    """测试统计功能"""
    print("测试统计功能...")
    
    trade_model = TradeModel()
    trades_df = trade_model.get_all_trades()
    
    if not trades_df.empty:
        stats = TradingCalculator.calculate_statistics(trades_df)
        print("交易统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
    else:
        print("没有交易数据进行统计")
    
    print("统计测试完成！\n")

def main():
    """主测试函数"""
    print("=" * 50)
    print("交易复盘工具 - 功能测试")
    print("=" * 50)
    
    try:
        test_database()
        test_calculator()
        test_statistics()
        
        print("=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
