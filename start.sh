#!/bin/bash
# 交易复盘工具启动脚本

echo "正在启动交易复盘工具..."

# 检查Python版本
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "错误: 未找到Python解释器"
    exit 1
fi

# 检查依赖
echo "检查依赖项..."
$PYTHON_CMD -c "import pandas" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装pandas..."
    pip3 install pandas
fi

# 设置环境变量以抑制Tkinter警告
export TK_SILENCE_DEPRECATION=1

# 启动应用程序
echo "启动应用程序..."
$PYTHON_CMD main.py
