#!/bin/bash
# 交易复盘工具启动脚本

echo "正在启动交易复盘工具..."

# 检查Python版本
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "错误: 未找到Python解释器"
    exit 1
fi

# 检查依赖
echo "检查依赖项..."
$PYTHON_CMD -c "import pandas" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装pandas..."
    pip3 install pandas
fi

# 设置环境变量以抑制Tkinter警告
export TK_SILENCE_DEPRECATION=1

# 在macOS上，确保GUI应用程序能正确显示
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "检测到macOS系统，正在优化GUI显示..."
    # 使用pythonw来启动GUI应用程序（如果可用）
    if command -v pythonw &> /dev/null; then
        PYTHON_CMD="pythonw"
    fi
fi

# 启动应用程序
echo "启动应用程序..."
echo "如果窗口没有显示，请检查Dock中的Python图标"
$PYTHON_CMD main.py &

# 等待一下，然后提示用户
sleep 2
echo "应用程序已启动！"
echo "如果看不到窗口，请："
echo "1. 检查Dock中是否有Python图标"
echo "2. 尝试按Command+Tab切换应用程序"
echo "3. 或者直接运行: python3 simple_test.py 进行测试"
