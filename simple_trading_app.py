#!/usr/bin/env python3
"""
简化版交易复盘工具
"""
import tkinter as tk
from tkinter import ttk
import os

class SimpleTradingApp:
    def __init__(self):
        # 设置环境
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("交易复盘工具 - 简化版")
        self.root.geometry("1000x700")
        
        # 创建界面
        self.create_interface()
        
    def create_interface(self):
        """创建界面"""
        # 主标题
        title_label = tk.Label(self.root, text="交易复盘工具", 
                              font=("Arial", 20, "bold"), 
                              bg="lightblue", fg="darkblue")
        title_label.pack(fill=tk.X, pady=10)
        
        # 创建标签页
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加交易标签页
        self.create_add_trade_tab(notebook)
        
        # 交易历史标签页
        self.create_history_tab(notebook)
        
        # 状态栏
        status_frame = tk.Frame(self.root, relief=tk.SUNKEN, bd=1)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = tk.Label(status_frame, text="就绪", anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
    def create_add_trade_tab(self, notebook):
        """创建添加交易标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="添加交易")
        
        # 标题
        tk.Label(frame, text="添加新交易", font=("Arial", 16, "bold")).pack(pady=10)
        
        # 输入区域
        input_frame = tk.Frame(frame)
        input_frame.pack(pady=20)
        
        # 仓位大小
        tk.Label(input_frame, text="仓位大小:", font=("Arial", 12)).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.position_entry = tk.Entry(input_frame, font=("Arial", 12), width=20)
        self.position_entry.grid(row=0, column=1, padx=10, pady=5)
        
        # 交易方向
        tk.Label(input_frame, text="交易方向:", font=("Arial", 12)).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.direction_var = tk.StringVar()
        direction_combo = ttk.Combobox(input_frame, textvariable=self.direction_var, 
                                     values=["多头", "空头"], state="readonly", width=17)
        direction_combo.grid(row=1, column=1, padx=10, pady=5)
        
        # 止盈百分比
        tk.Label(input_frame, text="止盈百分比:", font=("Arial", 12)).grid(row=2, column=0, sticky=tk.W, pady=5)
        self.take_profit_entry = tk.Entry(input_frame, font=("Arial", 12), width=20)
        self.take_profit_entry.grid(row=2, column=1, padx=10, pady=5)
        
        # 止损百分比
        tk.Label(input_frame, text="止损百分比:", font=("Arial", 12)).grid(row=3, column=0, sticky=tk.W, pady=5)
        self.stop_loss_entry = tk.Entry(input_frame, font=("Arial", 12), width=20)
        self.stop_loss_entry.grid(row=3, column=1, padx=10, pady=5)
        
        # 按钮
        button_frame = tk.Frame(frame)
        button_frame.pack(pady=20)
        
        save_btn = tk.Button(button_frame, text="保存交易", font=("Arial", 12), 
                           bg="lightgreen", command=self.save_trade)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        clear_btn = tk.Button(button_frame, text="清空表单", font=("Arial", 12), 
                            bg="lightcoral", command=self.clear_form)
        clear_btn.pack(side=tk.LEFT, padx=10)
        
    def create_history_tab(self, notebook):
        """创建交易历史标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="交易历史")
        
        # 标题
        tk.Label(frame, text="交易历史记录", font=("Arial", 16, "bold")).pack(pady=10)
        
        # 列表区域
        list_frame = tk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建列表
        columns = ('仓位大小', '方向', '止盈%', '止损%')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加一些示例数据
        self.tree.insert('', 'end', values=('1000', '多头', '2.0', '1.0'))
        self.tree.insert('', 'end', values=('1500', '空头', '1.5', '0.8'))
        
    def save_trade(self):
        """保存交易"""
        position = self.position_entry.get()
        direction = self.direction_var.get()
        take_profit = self.take_profit_entry.get()
        stop_loss = self.stop_loss_entry.get()
        
        if position and direction and take_profit and stop_loss:
            # 添加到列表
            self.tree.insert('', 'end', values=(position, direction, take_profit, stop_loss))
            self.status_label.config(text=f"已保存交易: {position} {direction}")
            self.clear_form()
        else:
            self.status_label.config(text="请填写所有必填字段")
    
    def clear_form(self):
        """清空表单"""
        self.position_entry.delete(0, tk.END)
        self.direction_var.set("")
        self.take_profit_entry.delete(0, tk.END)
        self.stop_loss_entry.delete(0, tk.END)
        
    def run(self):
        """运行应用程序"""
        print("启动简化版交易复盘工具...")
        self.root.mainloop()

def main():
    app = SimpleTradingApp()
    app.run()

if __name__ == "__main__":
    main()
