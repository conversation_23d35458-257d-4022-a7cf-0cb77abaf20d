#!/usr/bin/env python3
"""
最小化的交易复盘工具 - 确保GUI能正常显示
"""
import tkinter as tk
from tkinter import ttk, messagebox
import os

# 设置环境变量
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def create_minimal_app():
    """创建最小化的应用程序"""
    root = tk.Tk()
    root.title("交易复盘工具 - 简化版")
    root.geometry("800x600")
    
    # 强制窗口显示在最前面
    root.lift()
    root.attributes('-topmost', True)
    root.after(1000, lambda: root.attributes('-topmost', False))
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="🎯 交易复盘工具", font=('Arial', 20, 'bold'))
    title_label.pack(pady=20)
    
    # 说明文本
    info_text = tk.Text(main_frame, height=15, width=80, wrap=tk.WORD)
    info_text.pack(pady=10, fill=tk.BOTH, expand=True)
    
    info_content = """
🎉 恭喜！GUI界面正常工作！

这是交易复盘工具的简化版本。如果您能看到这个窗口，说明Tkinter GUI功能正常。

主要功能包括：
✅ 交易记录管理
✅ 统计分析
✅ 数据导出
✅ 设置管理

要运行完整版本，请：
1. 关闭这个窗口
2. 在终端运行：python3 main.py
3. 或使用启动脚本：./start.sh

如果完整版本仍然看不到窗口，可能的原因：
- 窗口被其他应用程序遮挡
- 需要在Dock中查找Python图标
- 尝试按Command+Tab切换应用程序

技术信息：
- Python版本：3.9+
- GUI框架：Tkinter
- 数据库：SQLite
- 平台：跨平台支持
    """
    
    info_text.insert(1.0, info_content)
    info_text.config(state=tk.DISABLED)
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=20)
    
    # 测试按钮
    def test_message():
        messagebox.showinfo("测试成功", "GUI功能正常工作！\n\n您可以安全地使用完整版应用程序。")
    
    ttk.Button(button_frame, text="测试消息框", command=test_message).pack(side=tk.LEFT, padx=10)
    
    # 退出按钮
    ttk.Button(button_frame, text="退出", command=root.quit).pack(side=tk.LEFT, padx=10)
    
    # 启动完整版按钮
    def launch_full_app():
        messagebox.showinfo("启动提示", "请在终端运行：python3 main.py\n或使用启动脚本：./start.sh")
    
    ttk.Button(button_frame, text="启动完整版", command=launch_full_app).pack(side=tk.LEFT, padx=10)
    
    # 强制获得焦点
    root.focus_force()
    
    print("=" * 60)
    print("🎯 交易复盘工具 - 简化版已启动")
    print("=" * 60)
    print("如果您能看到GUI窗口，说明一切正常！")
    print("请查看屏幕上的应用程序窗口。")
    print("=" * 60)
    
    return root

if __name__ == "__main__":
    app = create_minimal_app()
    app.mainloop()
