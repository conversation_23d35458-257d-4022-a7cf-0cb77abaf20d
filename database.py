"""
数据库架构和连接管理模块
"""
import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="trading_review.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """初始化数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    position_size REAL NOT NULL,
                    direction TEXT NOT NULL CHECK(direction IN ('多头', '空头')),
                    take_profit_pct REAL NOT NULL,
                    stop_loss_pct REAL NOT NULL,
                    entry_time DATETIME NOT NULL,
                    exit_time DATETIME NOT NULL,
                    entry_fee_pct REAL NOT NULL,
                    exit_fee_pct REAL NOT NULL,
                    instrument TEXT,
                    strategy TEXT,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建用户设置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL
                )
            ''')
            
            # 插入默认设置
            default_settings = [
                ('default_position_size', '1000'),
                ('default_entry_fee', '0.1'),
                ('default_exit_fee', '0.1'),
                ('default_take_profit', '2.0'),
                ('default_stop_loss', '1.0')
            ]
            
            for key, value in default_settings:
                cursor.execute('''
                    INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)
                ''', (key, value))
            
            conn.commit()
    
    def execute_query(self, query, params=None):
        """执行查询并返回结果"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
    
    def execute_update(self, query, params=None):
        """执行更新操作"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.lastrowid
